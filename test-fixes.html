<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Y.js Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .editor-section {
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .log {
            height: 300px;
            overflow-y: auto;
            background: #f5f5f5;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
        }
        .log-timestamp {
            color: #666;
        }
        .log-success {
            color: #28a745;
        }
        .log-error {
            color: #dc3545;
        }
        .log-warning {
            color: #ffc107;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Y.js Fixes Test</h1>
    <p>This page tests the fixes for Y.js double import and redundant observer updates.</p>
    
    <div class="container">
        <div class="editor-section">
            <h3>Editor 1</h3>
            <input type="text" id="docId1" placeholder="Document ID" value="test-doc">
            <input type="text" id="userId1" placeholder="User ID" value="user1">
            <button id="connect1">Connect</button>
            <button id="disconnect1" disabled>Disconnect</button>
            <div id="status1" class="status disconnected">Disconnected</div>
            <textarea id="editor1" placeholder="Type here..." disabled></textarea>
            <div id="log1" class="log"></div>
        </div>
        
        <div class="editor-section">
            <h3>Editor 2</h3>
            <input type="text" id="docId2" placeholder="Document ID" value="test-doc">
            <input type="text" id="userId2" placeholder="User ID" value="user2">
            <button id="connect2">Connect</button>
            <button id="disconnect2" disabled>Disconnect</button>
            <div id="status2" class="status disconnected">Disconnected</div>
            <textarea id="editor2" placeholder="Type here..." disabled></textarea>
            <div id="log2" class="log"></div>
        </div>
    </div>

    <!-- Load YJS and y-websocket from CDN with double import prevention -->
    <script type="module">
        // Check if Y.js is already loaded to prevent double imports
        if (!window.Y) {
            const Y = await import('https://cdn.skypack.dev/yjs@13.6.10');
            const { WebsocketProvider } = await import('https://cdn.skypack.dev/y-websocket@1.4.5');
            
            window.Y = Y;
            window.WebsocketProvider = WebsocketProvider;
            
            console.log('✅ Y.js libraries loaded successfully');
        } else {
            console.log('⚠️ Y.js already loaded, skipping import');
        }

        // Dispatch event when libraries are loaded
        window.dispatchEvent(new CustomEvent('libraries-loaded'));
    </script>
    
    <script>
        class TestClient {
            constructor(editorId, logId, statusId, connectId, disconnectId, docIdId, userIdId) {
                this.editorEl = document.getElementById(editorId);
                this.logEl = document.getElementById(logId);
                this.statusEl = document.getElementById(statusId);
                this.connectBtn = document.getElementById(connectId);
                this.disconnectBtn = document.getElementById(disconnectId);
                this.docIdEl = document.getElementById(docIdId);
                this.userIdEl = document.getElementById(userIdId);
                
                this.doc = null;
                this.text = null;
                this.provider = null;
                this.connected = false;
                
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                
                this.editorEl.addEventListener('input', (e) => {
                    if (this.text) {
                        this.handleTextChange(e);
                    }
                });
            }
            
            handleTextChange(e) {
                const newContent = e.target.value;
                this.log(`📝 Editor input: "${newContent}" - Starting transaction`);

                // Use specific origin to identify local changes
                this.doc.transact(() => {
                    if (this.text.length > 0) {
                        this.text.delete(0, this.text.length);
                    }
                    if (newContent.length > 0) {
                        this.text.insert(0, newContent);
                    }
                }, 'local-input');

                this.log(`✅ Text updated in transaction: "${this.text.toString()}"`);
            }
            
            connect() {
                if (typeof Y === 'undefined' || typeof WebsocketProvider === 'undefined') {
                    this.log('❌ Required libraries not loaded!', 'error');
                    return;
                }
                
                const documentId = this.docIdEl.value.trim();
                const userId = this.userIdEl.value.trim();
                
                if (!documentId || !userId) {
                    alert('Please enter both Document ID and User ID');
                    return;
                }
                
                this.log('🔌 Connecting to y-websocket server...');
                
                // Initialize YJS document
                this.doc = new Y.Doc();
                this.text = this.doc.getText('content');
                
                // Create WebSocket provider
                const wsUrl = `ws://localhost:3000`;
                this.provider = new WebsocketProvider(wsUrl, documentId, this.doc);
                
                this.setupProviderListeners();
                this.setupDocumentListeners();
            }
            
            setupProviderListeners() {
                this.provider.on('status', event => {
                    this.log(`📡 Connection status: ${event.status}`);
                    if (event.status === 'connected') {
                        this.updateConnectionStatus(true);
                        this.log('✅ Connected to y-websocket server', 'success');
                        
                        // Update editor with current document content
                        setTimeout(() => {
                            this.editorEl.value = this.text.toString();
                            this.log('📄 Initial document content loaded');
                        }, 100);
                    } else if (event.status === 'disconnected') {
                        this.updateConnectionStatus(false);
                        this.log('❌ Disconnected from server', 'error');
                    }
                });
            }
            
            setupDocumentListeners() {
                // Observe text changes for real-time sync with improved logic
                this.text.observe((event, transaction) => {
                    const newContent = this.text.toString();
                    this.log(`🔍 Text observer triggered. Origin: ${JSON.stringify(transaction.origin)}, Content: "${newContent}"`);
                    this.log(`🔍 Current editor value: "${this.editorEl.value}"`);

                    // Only update editor if this change came from remote (not from local input)
                    if (transaction.origin !== 'local-input') {
                        if (this.editorEl.value !== newContent) {
                            this.editorEl.value = newContent;
                            this.log(`📝 Text synced from remote: "${newContent}"`);
                        } else {
                            this.log(`⚠️ Content already matches: editor="${this.editorEl.value}" vs new="${newContent}"`);
                        }
                    } else {
                        this.log(`🚫 Ignoring local change (origin is local-input)`);
                    }
                });
            }
            
            disconnect() {
                if (this.provider) {
                    this.provider.destroy();
                    this.provider = null;
                }
                this.updateConnectionStatus(false);
                this.log('🔌 Disconnected by user');
            }
            
            updateConnectionStatus(connected) {
                this.connected = connected;
                this.statusEl.textContent = connected ? 
                    `Connected to ${this.docIdEl.value} as ${this.userIdEl.value}` : 
                    'Disconnected';
                this.statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
                this.connectBtn.disabled = connected;
                this.disconnectBtn.disabled = !connected;
                this.editorEl.disabled = !connected;
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                
                const timestampSpan = document.createElement('span');
                timestampSpan.className = 'log-timestamp';
                timestampSpan.textContent = `[${timestamp}] `;
                
                const messageSpan = document.createElement('span');
                messageSpan.textContent = message;
                messageSpan.className = `log-${type}`;
                
                logEntry.appendChild(timestampSpan);
                logEntry.appendChild(messageSpan);
                
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
        }
        
        // Initialize clients after libraries load
        let client1, client2;

        function initializeClients() {
            client1 = new TestClient('editor1', 'log1', 'status1', 'connect1', 'disconnect1', 'docId1', 'userId1');
            client2 = new TestClient('editor2', 'log2', 'status2', 'connect2', 'disconnect2', 'docId2', 'userId2');
            
            console.log('✅ Test clients initialized');
        }

        // Wait for libraries to load
        window.addEventListener('libraries-loaded', initializeClients);

        // Fallback for page load
        window.addEventListener('load', () => {
            if (typeof Y !== 'undefined' && typeof WebsocketProvider !== 'undefined' && !client1) {
                initializeClients();
            }
        });
    </script>
</body>
</html>
