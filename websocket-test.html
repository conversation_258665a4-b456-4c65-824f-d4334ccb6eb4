<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .log {
            height: 200px;
            overflow-y: scroll;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Basic WebSocket Connection Test</h1>
    <p>This tests if basic WebSocket connections work before testing Y.js</p>
    
    <div id="status" class="status disconnected">Disconnected</div>
    <button id="connect">Connect</button>
    <button id="disconnect">Disconnect</button>
    <button id="send">Send Test Message</button>
    
    <h3>Log:</h3>
    <div id="log" class="log"></div>

    <script>
        let ws = null;
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const connectBtn = document.getElementById('connect');
        const disconnectBtn = document.getElementById('disconnect');
        const sendBtn = document.getElementById('send');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(connected) {
            statusEl.textContent = connected ? 'Connected' : 'Disconnected';
            statusEl.className = `status ${connected ? 'connected' : 'disconnected'}`;
            connectBtn.disabled = connected;
            disconnectBtn.disabled = !connected;
            sendBtn.disabled = !connected;
        }
        
        connectBtn.addEventListener('click', () => {
            log('Attempting to connect to WebSocket...');
            try {
                ws = new WebSocket('ws://localhost:3001/test-room');
                
                ws.onopen = () => {
                    log('WebSocket connection opened');
                    updateStatus(true);
                };
                
                ws.onmessage = (event) => {
                    log(`Received message: ${event.data}`);
                };
                
                ws.onclose = (event) => {
                    log(`WebSocket connection closed: ${event.code} ${event.reason}`);
                    updateStatus(false);
                };
                
                ws.onerror = (error) => {
                    log(`WebSocket error: ${error}`);
                    updateStatus(false);
                };
            } catch (error) {
                log(`Error creating WebSocket: ${error.message}`);
            }
        });
        
        disconnectBtn.addEventListener('click', () => {
            if (ws) {
                ws.close();
                ws = null;
            }
        });
        
        sendBtn.addEventListener('click', () => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = `Test message at ${new Date().toLocaleTimeString()}`;
                ws.send(message);
                log(`Sent: ${message}`);
            }
        });
        
        // Initialize
        updateStatus(false);
        log('WebSocket test page loaded');
    </script>
</body>
</html>
