# Development Dockerfile
FROM node:18-alpine

WORKDIR /app

# Install nodemon globally for development
RUN npm install -g nodemon

# Create logs directory
RUN mkdir -p logs

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Start with nodemon for development
CMD ["npm", "run", "dev"]
