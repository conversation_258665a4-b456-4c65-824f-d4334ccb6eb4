#!/usr/bin/env node

/**
 * Y-WebSocket Node.js Client Example
 * Demonstrates how to connect to the y-websocket server from Node.js
 */

const Y = require('yjs');
const { WebsocketProvider } = require('y-websocket');
const WebSocket = require('ws');

// Polyfill WebSocket for y-websocket
global.WebSocket = WebSocket;

class YWebSocketNodeClient {
  constructor(serverUrl = 'ws://localhost:3000', documentId = 'node-demo', userId = null) {
    this.serverUrl = serverUrl;
    this.documentId = documentId;
    this.userId = userId || `node-user-${Math.random().toString(36).substr(2, 9)}`;
    this.doc = null;
    this.provider = null;
    this.text = null;
    this.connected = false;
  }

  async connect() {
    console.log(`🔌 Connecting to ${this.serverUrl} as ${this.userId}...`);
    
    // Create YJS document
    this.doc = new Y.Doc();
    this.text = this.doc.getText('content');
    
    // Create WebSocket provider
    this.provider = new WebsocketProvider(this.serverUrl, this.documentId, this.doc);
    
    // Setup event listeners
    this.setupListeners();
    
    // Wait for connection
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 10000);
      
      this.provider.on('status', event => {
        if (event.status === 'connected') {
          clearTimeout(timeout);
          this.connected = true;
          console.log('✅ Connected successfully!');
          resolve();
        } else if (event.status === 'disconnected') {
          if (!this.connected) {
            clearTimeout(timeout);
            reject(new Error('Failed to connect'));
          } else {
            console.log('❌ Disconnected from server');
            this.connected = false;
          }
        }
      });
    });
  }

  setupListeners() {
    // Document change listener
    this.text.observe(event => {
      console.log('📄 Document updated:', this.text.toString());
    });

    // Awareness changes (other users)
    this.provider.awareness.on('change', changes => {
      const states = this.provider.awareness.getStates();
      const users = Array.from(states.values())
        .map(state => state.user?.name)
        .filter(name => name && name !== this.userId);
      
      console.log('👥 Connected users:', users.length > 0 ? users.join(', ') : 'None');
    });

    // Set local awareness
    this.provider.awareness.setLocalStateField('user', {
      name: this.userId,
      color: '#' + Math.floor(Math.random()*16777215).toString(16)
    });

    // Connection errors
    this.provider.on('connection-error', error => {
      console.error('❌ Connection error:', error.message);
    });
  }

  insertText(text, position = null) {
    if (!this.connected) {
      console.error('❌ Not connected to server');
      return;
    }

    const pos = position !== null ? position : this.text.length;
    this.text.insert(pos, text);
    console.log(`📝 Inserted "${text}" at position ${pos}`);
  }

  deleteText(position, length) {
    if (!this.connected) {
      console.error('❌ Not connected to server');
      return;
    }

    this.text.delete(position, length);
    console.log(`🗑️ Deleted ${length} characters at position ${position}`);
  }

  getText() {
    return this.text.toString();
  }

  getStats() {
    if (!this.connected) {
      return null;
    }

    return {
      documentId: this.documentId,
      userId: this.userId,
      textLength: this.text.length,
      connectedUsers: this.provider.awareness.getStates().size,
      connected: this.connected
    };
  }

  disconnect() {
    if (this.provider) {
      console.log('🔌 Disconnecting...');
      this.provider.destroy();
      this.provider = null;
      this.connected = false;
    }
  }
}

// Example usage
async function main() {
  const args = process.argv.slice(2);
  const serverUrl = args[0] || 'ws://localhost:3000';
  const documentId = args[1] || 'node-demo';
  const userId = args[2] || null;

  console.log('🚀 Y-WebSocket Node.js Client Example');
  console.log(`📄 Document: ${documentId}`);
  console.log(`🌐 Server: ${serverUrl}`);

  const client = new YWebSocketNodeClient(serverUrl, documentId, userId);

  try {
    await client.connect();
    
    // Example operations
    console.log('\n📝 Performing example operations...');
    
    // Insert some text
    client.insertText('Hello from Node.js client!\n');
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Insert more text
    client.insertText('This is a collaborative document.\n');
    
    // Show current content
    console.log('\n📄 Current document content:');
    console.log(client.getText());
    
    // Show stats
    console.log('\n📊 Stats:', client.getStats());
    
    // Keep running for demonstration
    console.log('\n⏳ Keeping connection alive for 30 seconds...');
    console.log('💡 Open the web client to see real-time collaboration!');
    
    setTimeout(() => {
      console.log('\n👋 Disconnecting...');
      client.disconnect();
      process.exit(0);
    }, 30000);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down gracefully...');
  process.exit(0);
});

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = YWebSocketNodeClient;
